// Gmail API REST implementation for email sending
import { GMAIL_CONFIG, validateGmailConfig } from '../config/gmail';

// Gmail API REST service class
class GmailAPIService {
  /**
   * UTF-8 safe base64url encoding
   */
  private static base64UrlEncode(str: string): string {
    // Convert string to UTF-8 bytes
    const utf8Bytes = new TextEncoder().encode(str);

    // Convert bytes to base64
    let base64 = '';
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';

    for (let i = 0; i < utf8Bytes.length; i += 3) {
      const a = utf8Bytes[i];
      const b = utf8Bytes[i + 1] || 0;
      const c = utf8Bytes[i + 2] || 0;

      const bitmap = (a << 16) | (b << 8) | c;

      base64 += chars.charAt((bitmap >> 18) & 63);
      base64 += chars.charAt((bitmap >> 12) & 63);
      base64 += i + 1 < utf8Bytes.length ? chars.charAt((bitmap >> 6) & 63) : '=';
      base64 += i + 2 < utf8Bytes.length ? chars.charAt(bitmap & 63) : '=';
    }

    // Convert to base64url format
    return base64
      .replace(/\+/g, '-')
      .replace(/\//g, '_')
      .replace(/=+$/, '');
  }

  private static async getAccessToken(): Promise<string> {
    try {
      const response = await fetch('https://oauth2.googleapis.com/token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          client_id: GMAIL_CONFIG.CLIENT_ID,
          client_secret: GMAIL_CONFIG.CLIENT_SECRET,
          refresh_token: GMAIL_CONFIG.REFRESH_TOKEN,
          grant_type: 'refresh_token',
        }),
      });

      const data = await response.json();
      return data.access_token;
    } catch (error) {
      console.error('Error getting access token:', error);
      throw error;
    }
  }

  static async sendEmail(to: string, subject: string, htmlBody: string): Promise<boolean> {
    try {
      const accessToken = await this.getAccessToken();

      // Sanitize inputs to prevent encoding issues
      const sanitizedTo = to.trim();
      const sanitizedSubject = subject.replace(/[\r\n]/g, ' ').trim();
      const sanitizedHtmlBody = htmlBody.trim();

      // Create email message in RFC 2822 format
      // Use authenticated Gmail address as sender to avoid SPF/DKIM issues
      const emailMessage = [
        `To: ${sanitizedTo}`,
        `From: ${GMAIL_CONFIG.FROM_EMAIL}`,
        `Reply-To: <EMAIL>`,
        `Subject: ${sanitizedSubject}`,
        'Content-Type: text/html; charset=utf-8',
        'MIME-Version: 1.0',
        '',
        sanitizedHtmlBody
      ].join('\r\n');

      // Encode message in base64url format (UTF-8 safe)
      const encodedMessage = this.base64UrlEncode(emailMessage);

      // Send email via Gmail API
      const response = await fetch('https://gmail.googleapis.com/gmail/v1/users/me/messages/send', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          raw: encodedMessage,
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Gmail API Response Error:', {
          status: response.status,
          statusText: response.statusText,
          body: errorText
        });
        throw new Error(`Gmail API error: ${response.status} ${response.statusText} - ${errorText}`);
      }

      const result = await response.json();
      console.log('Email sent successfully via Gmail API:', result.id);
      return true;
    } catch (error) {
      console.error('Error sending email via Gmail API:', error);

      // Log additional details for debugging
      if (error instanceof Error) {
        console.error('Error details:', {
          name: error.name,
          message: error.message,
          stack: error.stack
        });
      }

      return false;
    }
  }
}

// HTML Email Templates
class EmailTemplates {
  /**
   * Sanitize data to prevent encoding issues
   */
  private static sanitizeData(data: any): any {
    const sanitized: any = {};
    for (const [key, value] of Object.entries(data)) {
      if (typeof value === 'string') {
        // Replace problematic characters and ensure proper encoding
        sanitized[key] = value
          .replace(/[\u0000-\u001F\u007F-\u009F]/g, '') // Remove control characters
          .trim();
      } else {
        sanitized[key] = value;
      }
    }
    return sanitized;
  }

  static createBookingEmailHTML(data: any): string {
    const sanitizedData = this.sanitizeData(data);
    return `
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>New Booking Confirmation - Warriors of Africa Safari</title>
        <link href="https://fonts.googleapis.com/css2?family=Cormorant+Garamond:ital,wght@0,300;0,400;0,500;0,600;0,700;1,300;1,400;1,500;1,600;1,700&family=Open+Sans:ital,wght@0,300;0,400;0,500;0,600;0,700;1,300;1,400;1,500;1,600;1,700&display=swap" rel="stylesheet">
        <style>
          /* Reset and base styles */
          * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
          }

          body {
            font-family: 'Open Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #1F2937;
            background: linear-gradient(135deg, #0F0F0F 0%, #1A1A1A 25%, #0F0F0F 50%, #1A1A1A 75%, #0F0F0F 100%);
            margin: 0;
            padding: 20px;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
          }

          /* Main container with luxury glass effect */
          .email-container {
            max-width: 650px;
            margin: 0 auto;
            background: linear-gradient(135deg,
              rgba(242, 238, 230, 0.98) 0%,
              rgba(242, 238, 230, 0.95) 50%,
              rgba(242, 238, 230, 0.98) 100%);
            border-radius: 16px;
            overflow: hidden;
            box-shadow:
              0 25px 50px rgba(0, 0, 0, 0.4),
              0 0 0 1px rgba(212, 194, 164, 0.3),
              inset 0 1px 0 rgba(255, 255, 255, 0.2);
            position: relative;
          }

          /* Luxury header with gradient */
          .email-header {
            background: linear-gradient(135deg, #D4C2A4 0%, #B8A082 50%, #A68B5B 100%);
            padding: 40px 30px;
            text-align: center;
            position: relative;
            overflow: hidden;
          }

          .email-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                        radial-gradient(circle at 70% 70%, rgba(255, 255, 255, 0.08) 0%, transparent 50%);
            pointer-events: none;
          }

          .email-header h1 {
            font-family: 'Cormorant Garamond', serif;
            font-size: 32px;
            font-weight: 600;
            color: #1F2937;
            margin-bottom: 8px;
            text-shadow: 0 1px 2px rgba(255, 255, 255, 0.3);
            position: relative;
            z-index: 1;
          }

          .email-header p {
            font-size: 16px;
            color: #374151;
            font-weight: 500;
            position: relative;
            z-index: 1;
          }

          /* Content area */
          .email-content {
            padding: 40px 30px;
            background: rgba(242, 238, 230, 0.95);
          }

          /* Luxury section headers */
          .section-header {
            font-family: 'Cormorant Garamond', serif;
            font-size: 24px;
            font-weight: 600;
            color: #1F2937;
            margin: 30px 0 20px 0;
            padding-bottom: 10px;
            border-bottom: 2px solid rgba(212, 194, 164, 0.3);
            position: relative;
          }

          .section-header:first-child {
            margin-top: 0;
          }

          /* Customer info card */
          .customer-info {
            background: linear-gradient(135deg,
              rgba(212, 194, 164, 0.15) 0%,
              rgba(212, 194, 164, 0.08) 50%,
              rgba(212, 194, 164, 0.15) 100%);
            border: 1px solid rgba(212, 194, 164, 0.3);
            border-radius: 12px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow:
              0 8px 25px rgba(0, 0, 0, 0.1),
              inset 0 1px 0 rgba(255, 255, 255, 0.2);
          }

          .customer-info h3 {
            font-family: 'Cormorant Garamond', serif;
            font-size: 20px;
            font-weight: 600;
            color: #1F2937;
            margin-bottom: 15px;
          }

          /* Booking details card */
          .booking-card {
            background: rgba(255, 255, 255, 0.8);
            border: 1px solid rgba(212, 194, 164, 0.2);
            border-radius: 12px;
            padding: 25px;
            margin: 20px 0;
            box-shadow:
              0 4px 15px rgba(0, 0, 0, 0.08),
              inset 0 1px 0 rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(10px);
          }

          /* Detail rows */
          .detail-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid rgba(212, 194, 164, 0.15);
          }

          .detail-row:last-child {
            border-bottom: none;
          }

          .detail-label {
            font-weight: 600;
            color: #374151;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
          }

          .detail-value {
            color: #1F2937;
            font-weight: 500;
            text-align: right;
            max-width: 60%;
          }

          /* Highlight important values */
          .highlight {
            background: linear-gradient(135deg, #D4C2A4 0%, #B8A082 100%);
            color: #1F2937;
            padding: 4px 12px;
            border-radius: 6px;
            font-weight: 600;
            box-shadow: 0 2px 8px rgba(212, 194, 164, 0.3);
          }

          /* Traveler details */
          .travelers-section {
            background: rgba(255, 255, 255, 0.6);
            border: 1px solid rgba(212, 194, 164, 0.2);
            border-radius: 12px;
            padding: 25px;
            margin: 20px 0;
          }

          .travelers-details {
            background: rgba(249, 250, 251, 0.8);
            border: 1px solid rgba(212, 194, 164, 0.15);
            border-radius: 8px;
            padding: 20px;
            font-family: 'Open Sans', monospace;
            font-size: 14px;
            line-height: 1.6;
            color: #374151;
            white-space: pre-wrap;
            overflow-x: auto;
          }

          /* Links styling */
          a {
            color: #A68B5B;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
          }

          a:hover {
            color: #8B7355;
            text-decoration: underline;
          }

          /* Footer */
          .email-footer {
            background: linear-gradient(135deg, #1F2937 0%, #374151 100%);
            color: #F9FAFB;
            padding: 30px;
            text-align: center;
          }

          .footer-content {
            margin-bottom: 20px;
          }

          .footer-content h3 {
            font-family: 'Cormorant Garamond', serif;
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 8px;
            color: #D4C2A4;
          }

          .footer-divider {
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(212, 194, 164, 0.3), transparent);
            margin: 20px 0;
          }

          .footer-note {
            font-size: 12px;
            color: #9CA3AF;
            line-height: 1.5;
          }

          /* Badge styling */
          .booking-badge {
            display: inline-block;
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
            color: white;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 15px;
          }

          /* Responsive design */
          @media (max-width: 600px) {
            body {
              padding: 10px;
            }

            .email-container {
              border-radius: 12px;
            }

            .email-header {
              padding: 30px 20px;
            }

            .email-header h1 {
              font-size: 26px;
            }

            .email-content {
              padding: 30px 20px;
            }

            .customer-info,
            .booking-card,
            .travelers-section {
              padding: 20px;
            }

            .detail-row {
              flex-direction: column;
              align-items: flex-start;
              gap: 5px;
            }

            .detail-value {
              text-align: left;
              max-width: 100%;
            }

            .email-footer {
              padding: 25px 20px;
            }
          }
        </style>
      </head>
      <body>
        <div class="email-container">
          <div class="email-header">
            <div class="booking-badge">New Booking Alert</div>
            <h1>Booking Confirmation</h1>
            <p>Warriors of Africa Safari - Premium Experience</p>
          </div>

          <div class="email-content">
            <div class="customer-info">
              <h3>Customer Contact Information</h3>
              <div class="detail-row">
                <span class="detail-label">Full Name</span>
                <span class="detail-value">${sanitizedData.customer_name}</span>
              </div>
              <div class="detail-row">
                <span class="detail-label">Email Address</span>
                <span class="detail-value"><a href="mailto:${sanitizedData.customer_email}">${sanitizedData.customer_email}</a></span>
              </div>
              <div class="detail-row">
                <span class="detail-label">Phone Number</span>
                <span class="detail-value"><a href="tel:${sanitizedData.customer_phone}">${sanitizedData.customer_phone}</a></span>
              </div>
            </div>

            <h2 class="section-header">Booking Details</h2>
            <div class="booking-card">
              <div class="detail-row">
                <span class="detail-label">Booking ID</span>
                <span class="detail-value highlight">${sanitizedData.booking_id}</span>
              </div>
              <div class="detail-row">
                <span class="detail-label">Safari Tour</span>
                <span class="detail-value">${sanitizedData.tour_title}</span>
              </div>
              <div class="detail-row">
                <span class="detail-label">Start Date</span>
                <span class="detail-value">${sanitizedData.start_date}</span>
              </div>
              <div class="detail-row">
                <span class="detail-label">Group Size</span>
                <span class="detail-value">${sanitizedData.group_size} adults</span>
              </div>
              <div class="detail-row">
                <span class="detail-label">Children</span>
                <span class="detail-value">${sanitizedData.children_count}</span>
              </div>
              <div class="detail-row">
                <span class="detail-label">Accommodation</span>
                <span class="detail-value">${sanitizedData.accommodation}</span>
              </div>
              <div class="detail-row">
                <span class="detail-label">Total Investment</span>
                <span class="detail-value highlight">${sanitizedData.total_price}</span>
              </div>
              <div class="detail-row">
                <span class="detail-label">Add-ons</span>
                <span class="detail-value">${sanitizedData.add_ons}</span>
              </div>
              <div class="detail-row">
                <span class="detail-label">Special Requests</span>
                <span class="detail-value">${sanitizedData.special_requests}</span>
              </div>
            </div>

            <h3 class="section-header">Traveler Information</h3>
            <div class="travelers-section">
              <div class="travelers-details">${sanitizedData.travelers_details}</div>
            </div>

            <div style="text-align: center; margin-top: 30px; padding: 20px; background: rgba(212, 194, 164, 0.1); border-radius: 8px;">
              <p style="margin: 0; color: #374151; font-weight: 500;">
                <strong>Booking Submitted:</strong> ${sanitizedData.booking_date} at ${sanitizedData.booking_time}
              </p>
            </div>
          </div>

          <div class="email-footer">
            <div class="footer-content">
              <h3>Warriors of Africa Safari</h3>
              <p>Premium African Safari Experiences</p>
              <p>Email: <a href="mailto:<EMAIL>" style="color: #D4C2A4;"><EMAIL></a></p>
            </div>
            <div class="footer-divider"></div>
            <div class="footer-note">
              This booking notification was sent automatically from your website.<br>
              Reply directly to this email to contact the customer immediately.
            </div>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  static createCustomTourEmailHTML(data: any): string {
    const sanitizedData = this.sanitizeData(data);
    return `
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>New Custom Tour Request - Warriors of Africa Safari</title>
        <link href="https://fonts.googleapis.com/css2?family=Cormorant+Garamond:ital,wght@0,300;0,400;0,500;0,600;0,700;1,300;1,400;1,500;1,600;1,700&family=Open+Sans:ital,wght@0,300;0,400;0,500;0,600;0,700;1,300;1,400;1,500;1,600;1,700&display=swap" rel="stylesheet">
        <style>
          /* Reset and base styles */
          * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
          }

          body {
            font-family: 'Open Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #1F2937;
            background: linear-gradient(135deg, #0F0F0F 0%, #1A1A1A 25%, #0F0F0F 50%, #1A1A1A 75%, #0F0F0F 100%);
            margin: 0;
            padding: 20px;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
          }

          /* Main container with luxury glass effect */
          .email-container {
            max-width: 650px;
            margin: 0 auto;
            background: linear-gradient(135deg,
              rgba(242, 238, 230, 0.98) 0%,
              rgba(242, 238, 230, 0.95) 50%,
              rgba(242, 238, 230, 0.98) 100%);
            border-radius: 16px;
            overflow: hidden;
            box-shadow:
              0 25px 50px rgba(0, 0, 0, 0.4),
              0 0 0 1px rgba(212, 194, 164, 0.3),
              inset 0 1px 0 rgba(255, 255, 255, 0.2);
            position: relative;
          }

          /* Luxury header with gradient */
          .email-header {
            background: linear-gradient(135deg, #A68B5B 0%, #8B7355 50%, #6B5B47 100%);
            padding: 40px 30px;
            text-align: center;
            position: relative;
            overflow: hidden;
          }

          .email-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                        radial-gradient(circle at 70% 70%, rgba(255, 255, 255, 0.08) 0%, transparent 50%);
            pointer-events: none;
          }

          .email-header h1 {
            font-family: 'Cormorant Garamond', serif;
            font-size: 32px;
            font-weight: 600;
            color: #F9FAFB;
            margin-bottom: 8px;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            position: relative;
            z-index: 1;
          }

          .email-header p {
            font-size: 16px;
            color: #E5E7EB;
            font-weight: 500;
            position: relative;
            z-index: 1;
          }

          /* Content area */
          .email-content {
            padding: 40px 30px;
            background: rgba(242, 238, 230, 0.95);
          }

          /* Luxury section headers */
          .section-header {
            font-family: 'Cormorant Garamond', serif;
            font-size: 24px;
            font-weight: 600;
            color: #1F2937;
            margin: 30px 0 20px 0;
            padding-bottom: 10px;
            border-bottom: 2px solid rgba(212, 194, 164, 0.3);
            position: relative;
          }

          .section-header:first-child {
            margin-top: 0;
          }

          /* Customer info card */
          .customer-info {
            background: linear-gradient(135deg,
              rgba(212, 194, 164, 0.15) 0%,
              rgba(212, 194, 164, 0.08) 50%,
              rgba(212, 194, 164, 0.15) 100%);
            border: 1px solid rgba(212, 194, 164, 0.3);
            border-radius: 12px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow:
              0 8px 25px rgba(0, 0, 0, 0.1),
              inset 0 1px 0 rgba(255, 255, 255, 0.2);
          }

          .customer-info h3 {
            font-family: 'Cormorant Garamond', serif;
            font-size: 20px;
            font-weight: 600;
            color: #1F2937;
            margin-bottom: 15px;
          }

          /* Tour details card */
          .tour-card {
            background: rgba(255, 255, 255, 0.8);
            border: 1px solid rgba(212, 194, 164, 0.2);
            border-radius: 12px;
            padding: 25px;
            margin: 20px 0;
            box-shadow:
              0 4px 15px rgba(0, 0, 0, 0.08),
              inset 0 1px 0 rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(10px);
          }

          /* Detail rows */
          .detail-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid rgba(212, 194, 164, 0.15);
          }

          .detail-row:last-child {
            border-bottom: none;
          }

          .detail-label {
            font-weight: 600;
            color: #374151;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
          }

          .detail-value {
            color: #1F2937;
            font-weight: 500;
            text-align: right;
            max-width: 60%;
          }

          /* Highlight important values */
          .highlight {
            background: linear-gradient(135deg, #A68B5B 0%, #8B7355 100%);
            color: #F9FAFB;
            padding: 4px 12px;
            border-radius: 6px;
            font-weight: 600;
            box-shadow: 0 2px 8px rgba(166, 139, 91, 0.3);
          }

          /* Links styling */
          a {
            color: #A68B5B;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
          }

          a:hover {
            color: #8B7355;
            text-decoration: underline;
          }

          /* Footer */
          .email-footer {
            background: linear-gradient(135deg, #1F2937 0%, #374151 100%);
            color: #F9FAFB;
            padding: 30px;
            text-align: center;
          }

          .footer-content {
            margin-bottom: 20px;
          }

          .footer-content h3 {
            font-family: 'Cormorant Garamond', serif;
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 8px;
            color: #D4C2A4;
          }

          .footer-divider {
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(212, 194, 164, 0.3), transparent);
            margin: 20px 0;
          }

          .footer-note {
            font-size: 12px;
            color: #9CA3AF;
            line-height: 1.5;
          }

          /* Badge styling */
          .request-badge {
            display: inline-block;
            background: linear-gradient(135deg, #7C3AED 0%, #5B21B6 100%);
            color: white;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 15px;
          }

          /* Responsive design */
          @media (max-width: 600px) {
            body {
              padding: 10px;
            }

            .email-container {
              border-radius: 12px;
            }

            .email-header {
              padding: 30px 20px;
            }

            .email-header h1 {
              font-size: 26px;
            }

            .email-content {
              padding: 30px 20px;
            }

            .customer-info,
            .tour-card {
              padding: 20px;
            }

            .detail-row {
              flex-direction: column;
              align-items: flex-start;
              gap: 5px;
            }

            .detail-value {
              text-align: left;
              max-width: 100%;
            }

            .email-footer {
              padding: 25px 20px;
            }
          }
        </style>
      </head>
      <body>
        <div class="email-container">
          <div class="email-header">
            <div class="request-badge">Custom Tour Request</div>
            <h1>Bespoke Safari Inquiry</h1>
            <p>Warriors of Africa Safari - Tailored Experiences</p>
          </div>

          <div class="email-content">
            <div class="customer-info">
              <h3>Customer Contact Information</h3>
              <div class="detail-row">
                <span class="detail-label">Full Name</span>
                <span class="detail-value">${sanitizedData.customer_name}</span>
              </div>
              <div class="detail-row">
                <span class="detail-label">Email Address</span>
                <span class="detail-value"><a href="mailto:${sanitizedData.customer_email}">${sanitizedData.customer_email}</a></span>
              </div>
              <div class="detail-row">
                <span class="detail-label">Phone Number</span>
                <span class="detail-value"><a href="tel:${sanitizedData.customer_phone}">${sanitizedData.customer_phone}</a></span>
              </div>
            </div>

            <h2 class="section-header">Custom Tour Requirements</h2>
            <div class="tour-card">
              <div class="detail-row">
                <span class="detail-label">Request ID</span>
                <span class="detail-value highlight">${sanitizedData.request_id}</span>
              </div>
              <div class="detail-row">
                <span class="detail-label">Tour Duration</span>
                <span class="detail-value">${sanitizedData.duration}</span>
              </div>
              <div class="detail-row">
                <span class="detail-label">Participants</span>
                <span class="detail-value">${sanitizedData.participants} travelers</span>
              </div>
              <div class="detail-row">
                <span class="detail-label">Budget Range</span>
                <span class="detail-value highlight">${sanitizedData.budget_range}</span>
              </div>
              <div class="detail-row">
                <span class="detail-label">Preferred Start Date</span>
                <span class="detail-value">${sanitizedData.start_date}</span>
              </div>
              <div class="detail-row">
                <span class="detail-label">Destinations</span>
                <span class="detail-value">${sanitizedData.destinations}</span>
              </div>
              <div class="detail-row">
                <span class="detail-label">Interests</span>
                <span class="detail-value">${sanitizedData.interests}</span>
              </div>
              <div class="detail-row">
                <span class="detail-label">Accommodation</span>
                <span class="detail-value">${sanitizedData.accommodation}</span>
              </div>
              <div class="detail-row">
                <span class="detail-label">Activities</span>
                <span class="detail-value">${sanitizedData.activities}</span>
              </div>
              <div class="detail-row">
                <span class="detail-label">Fitness Level</span>
                <span class="detail-value">${sanitizedData.fitness_level}</span>
              </div>
              <div class="detail-row">
                <span class="detail-label">Photography Interest</span>
                <span class="detail-value">${sanitizedData.photography_interest}</span>
              </div>
              <div class="detail-row">
                <span class="detail-label">Special Requests</span>
                <span class="detail-value">${sanitizedData.special_requests}</span>
              </div>
            </div>

            <div style="text-align: center; margin-top: 30px; padding: 20px; background: rgba(212, 194, 164, 0.1); border-radius: 8px;">
              <p style="margin: 0; color: #374151; font-weight: 500;">
                <strong>Request Submitted:</strong> ${sanitizedData.request_date} at ${sanitizedData.request_time}
              </p>
            </div>
          </div>

          <div class="email-footer">
            <div class="footer-content">
              <h3>Warriors of Africa Safari</h3>
              <p>Bespoke African Safari Experiences</p>
              <p>Email: <a href="mailto:<EMAIL>" style="color: #D4C2A4;"><EMAIL></a></p>
            </div>
            <div class="footer-divider"></div>
            <div class="footer-note">
              This custom tour request was sent automatically from your website.<br>
              Reply directly to this email to contact the customer and begin crafting their perfect safari.
            </div>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  static createContactEmailHTML(data: any): string {
    const sanitizedData = this.sanitizeData(data);
    return `
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>New Contact Form Submission - Warriors of Africa Safari</title>
        <link href="https://fonts.googleapis.com/css2?family=Cormorant+Garamond:ital,wght@0,300;0,400;0,500;0,600;0,700;1,300;1,400;1,500;1,600;1,700&family=Open+Sans:ital,wght@0,300;0,400;0,500;0,600;0,700;1,300;1,400;1,500;1,600;1,700&display=swap" rel="stylesheet">
        <style>
          /* Reset and base styles */
          * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
          }

          body {
            font-family: 'Open Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #1F2937;
            background: linear-gradient(135deg, #0F0F0F 0%, #1A1A1A 25%, #0F0F0F 50%, #1A1A1A 75%, #0F0F0F 100%);
            margin: 0;
            padding: 20px;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
          }

          /* Main container with luxury glass effect */
          .email-container {
            max-width: 650px;
            margin: 0 auto;
            background: linear-gradient(135deg,
              rgba(242, 238, 230, 0.98) 0%,
              rgba(242, 238, 230, 0.95) 50%,
              rgba(242, 238, 230, 0.98) 100%);
            border-radius: 16px;
            overflow: hidden;
            box-shadow:
              0 25px 50px rgba(0, 0, 0, 0.4),
              0 0 0 1px rgba(212, 194, 164, 0.3),
              inset 0 1px 0 rgba(255, 255, 255, 0.2);
            position: relative;
          }

          /* Luxury header with gradient */
          .email-header {
            background: linear-gradient(135deg, #059669 0%, #047857 50%, #065F46 100%);
            padding: 40px 30px;
            text-align: center;
            position: relative;
            overflow: hidden;
          }

          .email-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                        radial-gradient(circle at 70% 70%, rgba(255, 255, 255, 0.08) 0%, transparent 50%);
            pointer-events: none;
          }

          .email-header h1 {
            font-family: 'Cormorant Garamond', serif;
            font-size: 32px;
            font-weight: 600;
            color: #F9FAFB;
            margin-bottom: 8px;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            position: relative;
            z-index: 1;
          }

          .email-header p {
            font-size: 16px;
            color: #E5E7EB;
            font-weight: 500;
            position: relative;
            z-index: 1;
          }

          /* Content area */
          .email-content {
            padding: 40px 30px;
            background: rgba(242, 238, 230, 0.95);
          }

          /* Luxury section headers */
          .section-header {
            font-family: 'Cormorant Garamond', serif;
            font-size: 24px;
            font-weight: 600;
            color: #1F2937;
            margin: 30px 0 20px 0;
            padding-bottom: 10px;
            border-bottom: 2px solid rgba(212, 194, 164, 0.3);
            position: relative;
          }

          .section-header:first-child {
            margin-top: 0;
          }

          /* Customer info card */
          .customer-info {
            background: linear-gradient(135deg,
              rgba(212, 194, 164, 0.15) 0%,
              rgba(212, 194, 164, 0.08) 50%,
              rgba(212, 194, 164, 0.15) 100%);
            border: 1px solid rgba(212, 194, 164, 0.3);
            border-radius: 12px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow:
              0 8px 25px rgba(0, 0, 0, 0.1),
              inset 0 1px 0 rgba(255, 255, 255, 0.2);
          }

          .customer-info h3 {
            font-family: 'Cormorant Garamond', serif;
            font-size: 20px;
            font-weight: 600;
            color: #1F2937;
            margin-bottom: 15px;
          }

          /* Contact details card */
          .contact-card {
            background: rgba(255, 255, 255, 0.8);
            border: 1px solid rgba(212, 194, 164, 0.2);
            border-radius: 12px;
            padding: 25px;
            margin: 20px 0;
            box-shadow:
              0 4px 15px rgba(0, 0, 0, 0.08),
              inset 0 1px 0 rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(10px);
          }

          /* Detail rows */
          .detail-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid rgba(212, 194, 164, 0.15);
          }

          .detail-row:last-child {
            border-bottom: none;
          }

          .detail-label {
            font-weight: 600;
            color: #374151;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
          }

          .detail-value {
            color: #1F2937;
            font-weight: 500;
            text-align: right;
            max-width: 60%;
          }

          /* Highlight important values */
          .highlight {
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
            color: #F9FAFB;
            padding: 4px 12px;
            border-radius: 6px;
            font-weight: 600;
            box-shadow: 0 2px 8px rgba(5, 150, 105, 0.3);
          }

          /* Message section */
          .message-section {
            background: linear-gradient(135deg,
              rgba(5, 150, 105, 0.08) 0%,
              rgba(5, 150, 105, 0.05) 50%,
              rgba(5, 150, 105, 0.08) 100%);
            border: 1px solid rgba(5, 150, 105, 0.2);
            border-left: 4px solid #059669;
            border-radius: 12px;
            padding: 25px;
            margin: 20px 0;
            box-shadow:
              0 4px 15px rgba(5, 150, 105, 0.1),
              inset 0 1px 0 rgba(255, 255, 255, 0.3);
          }

          .message-content {
            color: #1F2937;
            font-size: 16px;
            line-height: 1.7;
            white-space: pre-wrap;
            word-wrap: break-word;
          }

          /* Links styling */
          a {
            color: #059669;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
          }

          a:hover {
            color: #047857;
            text-decoration: underline;
          }

          /* Footer */
          .email-footer {
            background: linear-gradient(135deg, #1F2937 0%, #374151 100%);
            color: #F9FAFB;
            padding: 30px;
            text-align: center;
          }

          .footer-content {
            margin-bottom: 20px;
          }

          .footer-content h3 {
            font-family: 'Cormorant Garamond', serif;
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 8px;
            color: #D4C2A4;
          }

          .footer-divider {
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(212, 194, 164, 0.3), transparent);
            margin: 20px 0;
          }

          .footer-note {
            font-size: 12px;
            color: #9CA3AF;
            line-height: 1.5;
          }

          /* Badge styling */
          .contact-badge {
            display: inline-block;
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
            color: white;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 15px;
          }

          /* Responsive design */
          @media (max-width: 600px) {
            body {
              padding: 10px;
            }

            .email-container {
              border-radius: 12px;
            }

            .email-header {
              padding: 30px 20px;
            }

            .email-header h1 {
              font-size: 26px;
            }

            .email-content {
              padding: 30px 20px;
            }

            .customer-info,
            .contact-card,
            .message-section {
              padding: 20px;
            }

            .detail-row {
              flex-direction: column;
              align-items: flex-start;
              gap: 5px;
            }

            .detail-value {
              text-align: left;
              max-width: 100%;
            }

            .email-footer {
              padding: 25px 20px;
            }
          }
        </style>
      </head>
      <body>
        <div class="email-container">
          <div class="email-header">
            <div class="contact-badge">Contact Inquiry</div>
            <h1>New Message Received</h1>
            <p>Warriors of Africa Safari - Customer Communication</p>
          </div>

          <div class="email-content">
            <div class="customer-info">
              <h3>Customer Contact Information</h3>
              <div class="detail-row">
                <span class="detail-label">Full Name</span>
                <span class="detail-value">${sanitizedData.customer_name}</span>
              </div>
              <div class="detail-row">
                <span class="detail-label">Email Address</span>
                <span class="detail-value"><a href="mailto:${sanitizedData.customer_email}">${sanitizedData.customer_email}</a></span>
              </div>
              <div class="detail-row">
                <span class="detail-label">Phone Number</span>
                <span class="detail-value"><a href="tel:${sanitizedData.customer_phone}">${sanitizedData.customer_phone}</a></span>
              </div>
            </div>

            <h2 class="section-header">Inquiry Details</h2>
            <div class="contact-card">
              <div class="detail-row">
                <span class="detail-label">Subject</span>
                <span class="detail-value highlight">${sanitizedData.inquiry_subject}</span>
              </div>
              <div class="detail-row">
                <span class="detail-label">Category</span>
                <span class="detail-value">${sanitizedData.inquiry_category}</span>
              </div>
            </div>

            <h3 class="section-header">Customer Message</h3>
            <div class="message-section">
              <div class="message-content">${sanitizedData.message}</div>
            </div>

            <div style="text-align: center; margin-top: 30px; padding: 20px; background: rgba(212, 194, 164, 0.1); border-radius: 8px;">
              <p style="margin: 0; color: #374151; font-weight: 500;">
                <strong>Message Received:</strong> ${sanitizedData.contact_date} at ${sanitizedData.contact_time}
              </p>
            </div>
          </div>

          <div class="email-footer">
            <div class="footer-content">
              <h3>Warriors of Africa Safari</h3>
              <p>Premium Customer Service</p>
              <p>Email: <a href="mailto:<EMAIL>" style="color: #D4C2A4;"><EMAIL></a></p>
            </div>
            <div class="footer-divider"></div>
            <div class="footer-note">
              This contact form submission was sent automatically from your website.<br>
              Reply directly to this email to respond to the customer inquiry.
            </div>
          </div>
        </div>
      </body>
      </html>
    `;
  }
}

export interface BookingEmailData {
  tourTitle: string;
  customerName: string;
  customerEmail: string;
  customerPhone?: string;
  startDate: string;
  groupSize: number;
  childrenCount: number;
  accommodation: string;
  totalPrice: number;
  specialRequests?: string;
  bookingId: string;
  travelers: Array<{
    name: string;
    age: number;
    nationality: string;
    dietaryRequirements?: string;
  }>;
  addOns: string[];
}

export interface CustomTourEmailData {
  customerName: string;
  customerEmail: string;
  customerPhone: string;
  duration: number;
  participants: number;
  budget: number[];
  startDate: string;
  destinations: string[];
  interests: string[];
  accommodation: string;
  activities: string[];
  specialRequests: string;
  fitnessLevel: string;
  photographyInterest: boolean;
  requestId: string;
}

export class EmailService {
  /**
   * Validate Gmail configuration before sending emails
   */
  private static validateConfig(): boolean {
    return validateGmailConfig();
  }

  /**
   * Send booking confirmation email to admin
   */
  static async sendBookingNotification(bookingData: BookingEmailData): Promise<boolean> {
    if (!this.validateConfig()) {
      console.error('Gmail API configuration is invalid. Please check your credentials.');
      return false;
    }
    try {
      const templateData = {
        booking_id: bookingData.bookingId,
        tour_title: bookingData.tourTitle,
        customer_name: bookingData.customerName,
        customer_email: bookingData.customerEmail,
        customer_phone: bookingData.customerPhone || 'Not provided',
        start_date: bookingData.startDate,
        group_size: bookingData.groupSize,
        children_count: bookingData.childrenCount,
        accommodation: bookingData.accommodation,
        total_price: `$${bookingData.totalPrice ? bookingData.totalPrice.toLocaleString() : '0'}`,
        special_requests: bookingData.specialRequests || 'None',
        travelers_details: this.formatTravelersDetails(bookingData.travelers),
        add_ons: bookingData.addOns.length > 0 ? bookingData.addOns.join(', ') : 'None',
        booking_date: new Date().toLocaleDateString(),
        booking_time: new Date().toLocaleTimeString(),
      };

      const subject = `New Booking Confirmation - ${bookingData.tourTitle}`;
      const htmlBody = EmailTemplates.createBookingEmailHTML(templateData);

      const success = await GmailAPIService.sendEmail(
        GMAIL_CONFIG.TO_EMAIL,
        subject,
        htmlBody
      );

      if (success) {
        console.log('Booking email sent successfully via Gmail API');
        return true;
      } else {
        throw new Error('Failed to send email via Gmail API');
      }
    } catch (error) {
      console.error('Error sending booking email:', error);
      return false;
    }
  }

  /**
   * Send custom tour request email to admin
   */
  static async sendCustomTourNotification(tourData: CustomTourEmailData): Promise<boolean> {
    if (!this.validateConfig()) {
      console.error('Gmail API configuration is invalid. Please check your credentials.');
      return false;
    }

    try {
      const templateData = {
        request_id: tourData.requestId,
        customer_name: tourData.customerName,
        customer_email: tourData.customerEmail,
        customer_phone: tourData.customerPhone,
        duration: `${tourData.duration} days`,
        participants: tourData.participants,
        budget_range: `$${tourData.budget[0].toLocaleString()} - $${tourData.budget[1]?.toLocaleString() || tourData.budget[0].toLocaleString()}`,
        start_date: tourData.startDate,
        destinations: tourData.destinations.join(', '),
        interests: tourData.interests.join(', '),
        accommodation: tourData.accommodation,
        activities: tourData.activities.join(', '),
        special_requests: tourData.specialRequests || 'None',
        fitness_level: tourData.fitnessLevel,
        photography_interest: tourData.photographyInterest ? 'Yes' : 'No',
        request_date: new Date().toLocaleDateString(),
        request_time: new Date().toLocaleTimeString(),
      };

      const subject = `New Custom Tour Request - ${tourData.customerName}`;
      const htmlBody = EmailTemplates.createCustomTourEmailHTML(templateData);

      const success = await GmailAPIService.sendEmail(
        GMAIL_CONFIG.TO_EMAIL,
        subject,
        htmlBody
      );

      if (success) {
        console.log('Custom tour email sent successfully via Gmail API');
        return true;
      } else {
        throw new Error('Failed to send email via Gmail API');
      }
    } catch (error) {
      console.error('Error sending custom tour email:', error);
      return false;
    }
  }

  /**
   * Format travelers details for email
   */
  private static formatTravelersDetails(travelers: BookingEmailData['travelers']): string {
    if (!travelers || travelers.length === 0) {
      return 'No traveler details provided';
    }

    return travelers.map((traveler, index) => {
      return `Traveler ${index + 1}:
- Name: ${traveler.name}
- Age: ${traveler.age}
- Nationality: ${traveler.nationality}
- Dietary Requirements: ${traveler.dietaryRequirements || 'None'}`;
    }).join('\n\n');
  }

  /**
   * Send general contact email (for contact form submissions)
   */
  static async sendContactNotification(contactData: {
    name: string;
    email: string;
    phone?: string;
    subject: string;
    message: string;
    category: string;
  }): Promise<boolean> {
    if (!this.validateConfig()) {
      console.error('Gmail API configuration is invalid. Please check your credentials.');
      return false;
    }

    try {
      const templateData = {
        customer_name: contactData.name,
        customer_email: contactData.email,
        customer_phone: contactData.phone || 'Not provided',
        inquiry_subject: contactData.subject,
        inquiry_category: contactData.category,
        message: contactData.message,
        contact_date: new Date().toLocaleDateString(),
        contact_time: new Date().toLocaleTimeString(),
      };

      const subject = `New Contact Form Submission - ${contactData.subject}`;
      const htmlBody = EmailTemplates.createContactEmailHTML(templateData);

      const success = await GmailAPIService.sendEmail(
        GMAIL_CONFIG.TO_EMAIL,
        subject,
        htmlBody
      );

      if (success) {
        console.log('Contact email sent successfully via Gmail API');
        return true;
      } else {
        throw new Error('Failed to send email via Gmail API');
      }
    } catch (error) {
      console.error('Error sending contact email:', error);
      return false;
    }
  }
}
