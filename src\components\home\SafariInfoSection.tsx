import React from 'react';

const SafariInfoSection = () => {
  const styles = {
    root: {
      '--page': '#F3EEE7',
      '--card': '#E8DCCB',
      '--ink': '#1C1A18',
      '--muted': '#6E6459',
      '--rule': '#E6D7C6',
      '--accent': '#C86B3E',
      '--accent-d': '#A45731',
      '--arrow': '#E6DAC3',
      '--serif': '"Playfair Display", Georgia, "Times New Roman", serif',
      '--cormorant': '"Cormorant Garamond", Georgia, "Times New Roman", serif',
      '--sans': 'Inter, ui-sans-serif, system-ui, -apple-system, "Segoe UI", Roboto, Arial, sans-serif',
      '--wrap': '1180px',
    }
  };

  return (
    <div style={styles.root}>
      <style jsx>{`
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600&family=Playfair+Display:ital,wght@0,400;0,500;0,700;1,400&display=swap');

        .safari-section {
          background: var(--page);
          color: var(--ink);
          font-family: var(--sans);
          line-height: 1.6;
          font-size: 16px;
          -webkit-font-smoothing: antialiased;
          -moz-osx-font-smoothing: grayscale;
          text-rendering: optimizeLegibility;
          /* Match homepage section spacing */
          padding: 5.5rem 0 7rem;
        }

        .wrap {
          max-width: var(--wrap);
          margin: 0 auto;
          padding: 0 2rem;
        }

        .callout-wrap {
          display: grid;
          grid-template-columns: 1fr;
          place-items: center;
          margin-bottom: 5rem;
        }

        .callout {
          position: relative;
          background: var(--card);
          border: 1px solid var(--rule);
          padding: 2.8rem 3rem;
          width: clamp(560px, 60vw, 740px);
          box-shadow: 0 0.5px 0 rgba(0,0,0,.05);
          overflow: hidden;
        }

        .callout-art {
          position: absolute;
          top: -130px;
          right: -130px;
          width: 435px;
          height: auto;
          pointer-events: none;
          z-index: 0;
        }

        .callout-content {
          position: relative;
          z-index: 1;
        }

        .eyebrow {
          font-size: 0.72rem;
          letter-spacing: 0.28em;
          text-transform: uppercase;
          color: #8a7f72;
          margin: 0 0 1.25rem;
        }

        .callout-title {
          font-family: var(--cormorant);
          font-size: clamp(1.9rem, 1.2rem + 2vw, 2.6rem);
          line-height: 1.2;
          font-weight: 400;
          margin: 0 0 1.1rem;
          color: var(--ink);
        }

        .callout-title em {
          font-style: italic;
          font-weight: 400;
        }

        .body {
          margin: 0 0 1.7rem;
          color: var(--muted);
          max-width: 55ch;
          font-size: 1rem;
        }

        .btn {
          display: inline-block;
          background: var(--accent);
          color: #fff;
          text-decoration: none;
          font-weight: 600;
          letter-spacing: 0.06em;
          text-transform: uppercase;
          font-size: 0.78rem;
          padding: 0.9rem 1.15rem;
          border-radius: 3px;
          transition: transform 0.06s ease, background 0.18s ease, box-shadow 0.18s ease;
          box-shadow: 0 6px 18px rgba(200,107,62,0.18);
          border: none;
          cursor: pointer;
        }

        .btn:hover {
          background: var(--accent-d);
          transform: translateY(-1px);
        }

        .btn:active {
          transform: translateY(0);
        }

        .split {
          display: grid;
          grid-template-columns: 1.3fr 0.7fr;
          gap: clamp(2.5rem, 5.5vw, 5rem);
          align-items: start;
        }

        .hero {
          aspect-ratio: 5 / 4;
          overflow: hidden;
          background: #ddd;
        }

        .hero img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          object-position: center;
          border: 1px solid var(--rule);
        }

        .split-copy {
          display: grid;
          grid-auto-rows: max-content;
          align-content: start;
          position: relative;
          padding-right: 1rem;
        }

        .split-copy .eyebrow {
          margin-top: 0.25rem;
          margin-bottom: 1.6rem;
          color: #8b8074;
          letter-spacing: 0.28em;
        }

        .display {
          font-family: var(--cormorant);
          text-transform: uppercase;
          font-weight: 400;
          letter-spacing: 0.02em;
          line-height: 1.08;
          font-size: clamp(2.3rem, 2rem + 2.8vw, 3.9rem);
          margin: 0 0 2.25rem;
          max-width: 11ch;
        }

        .elegant-arrow {
          width: 81px;
          height: auto;
          display: block;
          margin-top: 0.25rem;
          margin-left: clamp(1.25rem, 6vw, 3rem);
        }

        .elegant-arrow path {
          fill: var(--arrow);
        }

        @media (max-width: 1024px) {
          .callout {
            width: clamp(540px, 68vw, 720px);
          }
        }

        @media (max-width: 900px) {
          .split {
            grid-template-columns: 1fr;
          }
          .hero {
            aspect-ratio: 4 / 3;
          }
          .elegant-arrow {
            margin-left: 0;
          }
        }

        @media (max-width: 560px) {
          .wrap {
            padding: 0 1.25rem;
          }
          .safari-section {
            padding: 3.75rem 0 5.5rem;
          }
          .callout-wrap {
            margin-bottom: 0.2rem; /* Reduce gap between sections on mobile */
          }
          .callout {
            padding: 2rem 1.5rem;
            width: 100%;
          }
          .callout-art {
            top: -20px;
            right: -10px;
            width: 200px; /* Smaller decorative SVG for mobile */
            height:200px
          }
          .callout-content {
            text-align: center; /* Center all content in the callout on mobile */
          }
          .btn {
            width: 100%; /* Make button wider on mobile */
            max-width: 280px; /* Set a reasonable maximum width */
            margin: 0 auto; /* Center the button */
            display: block; /* Make it a block element for centering */
          }
          /* Overlay the copy on top of the image on small screens */
          .split {
            grid-template-columns: 1fr;
            position: relative;
          }
          .hero {
            aspect-ratio: 3 / 4; /* Taller image on small screens */
            position: relative;
          }
          .split-copy {
            position: absolute;
            left: 0;
            right: 0;
            bottom: 0;
            padding: 2rem 1.25rem 1.25rem;
            color: #fff;
            background: linear-gradient(180deg, rgba(0,0,0,0) 0%, rgba(0,0,0,0.35) 30%, rgba(0,0,0,0.7) 100%);
            z-index: 2;
            text-align: center; /* Center content horizontally */
          }
          .split-copy .eyebrow {
            color: rgba(255,255,255,0.9);
            margin-bottom: 0.9rem;
          }
          .display {
            color: #fff;
            margin-bottom: 1.25rem;
            font-size: clamp(1.8rem, 1.5rem + 2vw, 2.5rem);
            line-height: 1.1;
            text-shadow: 0 2px 4px rgba(0,0,0,0.35);
            margin-left: auto;
            margin-right: auto;
            text-align: center;
          }
          .elegant-arrow {
            width: 45px; /* Smaller arrow for mobile */
            margin-top: 0.25rem;
            margin-left: auto;
            margin-right: auto; /* center under text */
          }
          .elegant-arrow path {
            fill: rgba(255,255,255,0.9);
          }
        }
      `}</style>

      <div className="safari-section">
        <div className="wrap">
          {/* Centered Callout Card */}
          <section className="callout-wrap">
            <article className="callout">
              {/* Decorative SVG */}
              <div className="callout-art" aria-hidden="true">
                <svg
                  width="435"
                  height="436"
                  viewBox="0 0 435 436"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    opacity="0.4"
                    d="M107.466 327.515C185.018 405.227 282.928 442.768 363.68 433.813C378.256 431.78 388.64 426.134 395.131 419.655L417.556 397.276C423.286 391.805 427.634 384.678 429.851 376.438L433.288 363.712C439.735 339.744 425.603 315.241 401.635 308.792L330.023 289.556C313.544 285.123 296.829 290.35 285.891 301.875L264.379 323.342C231.358 315.236 197.199 296.124 168.121 266.987C139.043 237.849 120.002 203.651 111.964 170.614L133.476 149.146C145.021 138.231 150.285 121.526 145.886 105.039L126.796 33.3872C120.399 9.4064 95.9233 -4.77854 71.9418 1.621L59.2095 5.03143C50.9652 7.23441 43.8313 11.5679 38.3454 17.2833L15.9205 39.6621C9.43 46.1392 3.76092 56.5118 1.69877 71.0835C-7.42398 151.818 29.914 249.803 107.466 327.515Z"
                    fill="#F2EEE6"
                  />
                </svg>
              </div>

              <div className="callout-content">
                <p className="eyebrow">24/7 Services</p>
                <h1 className="callout-title">
                  You need anything? <em>Call</em> our Experts
                </h1>
                <p className="body">
                  Planning your dream safari? Our team of experienced safari experts is here to help you
                  every step of the way—whether you're seeking insider tips, a custom package, or the best
                  time to visit. We'll tailor each moment to you.
                </p>
                <button className="btn" onClick={() => console.log('Call expert clicked')}>
                  Call our expert
                </button>
              </div>
            </article>
          </section>

          {/* Feature Split */}
          <section className="split">
            <figure className="hero">
              <img
                src="https://firebasestorage.googleapis.com/v0/b/warriorsofafricasafari-5bb0d.firebasestorage.app/o/imagez%2Fwater.webp?alt=media&token=68a37e1d-8448-4857-ba4c-b142edb4a0e0"
                alt="Waterfall cascading through lush green forest"
              />
            </figure>

            <article className="split-copy">
              <p className="eyebrow">Classic Safaris</p>
              <h2 className="display">Explore Our Classic Safaris</h2>

              {/* Elegant Arrow */}
              <svg
                className="elegant-arrow"
                width="81"
                height="220"
                viewBox="0 0 81 220"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                aria-hidden="true"
              >
                <path
                  d="M56.0775 0.067036C54.1662 0.405885 52.6182 12.4281 51.6694 34.3061C51.4713 38.8707 50.5092 44.6001 49.5318 47.0383C47.3098 52.5806 37.3923 72.1715 36.3253 73.1235C35.3896 73.9548 36.0028 70.4002 38.1366 62.6417C41.6755 49.7604 46.7537 20.971 46.8896 13.0195C46.9258 11.031 47.541 7.68053 48.2618 5.5697L49.5724 1.73435L47.288 5.66358C44.2636 10.8634 42.99 15.1547 38.2324 36.1732C29.3826 75.2694 22.8557 110.755 20.8459 130.701C19.855 140.534 20.8067 139.938 9.12917 138.03C-0.0835034 136.524 -1.01076 137.325 1.10224 144.956C2.03209 148.317 4.74427 155.584 7.12599 161.107C9.5075 166.629 14.3798 178.043 17.9526 186.468C31.1672 217.636 32.8071 220.379 37.7152 219.504C40.1382 219.075 42.0911 216.05 52.9166 195.961C79.1151 147.345 81.3301 142.793 79.9229 140.455C78.897 138.735 72.3289 138.227 65.9 139.361L60.6094 140.299L60.1075 133.953C59.5013 126.286 59.2333 115.835 59.4932 109.961C59.7856 103.319 59.6302 63.2098 59.2961 59.2049C59.1349 57.2514 58.8962 49.9076 58.7703 42.8842C58.6448 35.8614 58.4417 29.8742 58.3203 29.5823C58.1982 29.2899 58.1095 27.1031 58.1215 24.7254C58.1427 20.7377 58.0382 17.718 57.5984 9.57692C57.5095 7.89423 57.4865 5.00235 57.5518 3.15105C57.6448 0.468543 57.3477 -0.159245 56.0894 0.0656628L56.0775 0.067036ZM63.0414 156.359C59.4356 162.237 58.415 164.018 46.9184 184.498C34.7549 206.167 35.3701 205.259 33.8903 203.716C31.025 200.731 31.4875 196.728 35.3968 190.64C37.5043 187.358 42.1117 180.031 45.6346 174.357C49.1584 168.683 54.2945 160.614 57.0493 156.427C59.8032 152.238 61.7861 148.623 61.4551 148.394C60.8387 147.966 41.7777 175.57 34.4561 187.498C30.656 193.689 30.3031 194.001 29.4907 191.873C28.1624 188.386 28.7342 186.876 34.91 177.622C41.5394 167.686 51.3557 151.468 51.2162 150.683C50.8496 148.615 38.6388 165.971 30.2953 180.418C27.6116 185.066 26.8802 185.207 25.3821 181.394C24.2966 178.618 24.5975 177.772 28.6379 172.322C34.6544 164.207 47.7482 142.736 47.5079 141.382C47.2835 140.118 44.3683 144.479 33.0147 163.067C28.4881 170.476 24.4554 176.595 24.0541 176.666C23.3531 176.785 10.9377 150.021 10.4847 147.418C10.1678 145.603 13.3672 145.324 20.147 146.571C27.3179 147.894 26.1901 148.969 36.9746 130.531C44.1302 118.296 49.0201 111.745 50.3178 112.652C51.5476 113.51 49.8224 120.407 47.4191 124.239C44.5 128.893 33.934 148.1 33.2115 150.062C31.9417 153.52 35.3769 149.407 37.77 144.589C41.7502 136.603 48.212 126.052 49.2355 125.871C50.5727 125.635 50.4807 134.074 49.1208 136.24C47.6569 138.574 47.7985 139.605 49.4491 138.614C50.3501 138.077 51.0879 139.02 52.0011 141.882C52.7091 144.098 53.5662 146.106 53.9068 146.345C54.2427 146.576 57.7661 146.717 61.7262 146.638C69.8913 146.481 69.7513 145.449 63.0465 156.383L63.0414 156.359ZM50.041 61.3601C49.5273 63.1502 48.089 66.1843 46.8461 68.1036C44.7503 71.3398 41.776 77.3842 35.6489 90.8594L33.1925 96.2619L35.7189 92.2102C37.1087 89.9823 39.555 85.3787 41.1551 81.9815C45.2542 73.2753 49.4349 66.5444 50.3462 67.1807C51.5609 68.029 50.2246 75.4667 48.2836 78.6576C47.3195 80.2446 43.8563 86.8816 40.5879 93.4053C37.3197 99.9315 34.2959 105.92 33.8686 106.718C33.301 107.768 33.4399 107.938 34.3814 107.334C35.3789 106.677 42.6732 93.6149 45.8628 86.7541C46.1431 86.141 47.4012 84.7096 48.6489 83.5669L50.9178 81.4891L51.0152 84.7152C51.1332 88.5482 48.7511 94.7132 44.6058 101.309C42.9498 103.943 40.2079 108.894 38.5137 112.309C35.4708 118.44 26.454 132.945 26.2884 131.977C26.0848 130.837 29.702 115.339 30.3958 114.338C30.8557 113.69 30.712 112.881 30.0425 112.315C29.3409 111.722 29.4609 109.009 30.3624 104.976C31.1471 101.455 31.8365 97.8706 31.8953 97.0102C32.4454 88.9555 36.4339 75.7915 40.6292 68.1813C42.3591 65.0429 44.9963 59.8364 46.4884 56.6101C47.9811 53.3848 49.6199 50.9119 50.1318 51.114C51.3099 51.5809 51.2591 57.1227 50.0436 61.3638L50.041 61.3601ZM49.4901 104.09C48.482 106.867 46.1799 111.517 44.3739 114.424C42.5685 117.331 38.9808 123.482 36.4012 128.093C28.7727 141.728 27.526 143.63 26.6282 143.001C24.7862 141.714 27.8822 131.965 32.2649 125.25C34.7714 121.409 38.3517 115.446 40.2203 112.002C45.2611 102.708 49.1364 97.0541 50.1825 97.4695C51.5551 98.0188 51.4968 98.565 49.4901 104.09Z"
                  fill="#E6DAC3"
                />
              </svg>
            </article>
          </section>
        </div>
      </div>
    </div>
  );
};

export default SafariInfoSection;
