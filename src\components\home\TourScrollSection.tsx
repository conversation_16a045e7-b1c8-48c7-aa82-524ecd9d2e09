import React, { useRef, useEffect, useState } from 'react';
import { Loader2, AlertTriangle } from 'lucide-react';
import { FirebaseService } from '@/services/firebase';
import { Tour } from '@/types/firebase';
import PageLoader from '@/components/ui/PageLoader';

const TourScrollSection = () => {
  const cardsPanelRef = useRef<HTMLDivElement>(null);
  const cardsWrapperRef = useRef<HTMLDivElement>(null);
  const [isMobile, setIsMobile] = useState(false);
  const [tours, setTours] = useState<Tour[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch tours from Firebase
  useEffect(() => {
    const fetchTours = async () => {
      try {
        setLoading(true);
        const allToursData = await FirebaseService.getTours();
        console.log('Raw tours data from Firebase:', allToursData);

        // Map the data properly like in FeaturedTours
        const allTours = allToursData.map((tour: any) => ({
          id: tour.id,
          title: tour.title || 'Untitled Tour',
          description: tour.description || 'No description available',
          price: tour.price || 0,
          duration: tour.duration || 'Duration not specified',
          location: tour.location || '',
          destinations: Array.isArray(tour.destinations) ? tour.destinations : [],
          activities: Array.isArray(tour.activities) ? tour.activities : [],
          accommodations: Array.isArray(tour.accommodations) ? tour.accommodations : [],
          maxGroupSize: tour.maxGroupSize || 12,
          minGroupSize: tour.minGroupSize || 1,
          difficulty: tour.difficulty || 'easy',
          includes: Array.isArray(tour.includes) ? tour.includes : [],
          excludes: Array.isArray(tour.excludes) ? tour.excludes : [],
          images: Array.isArray(tour.images) ? tour.images : [],
          featured: tour.featured || false,
          status: tour.status || 'active',
          rating: tour.rating || 0,
          reviewCount: tour.reviewCount || 0,
          tourType: tour.tourType || 'standard',
          category: tour.category || 'Safari',
          accommodationLevel: tour.accommodationLevel || 'Standard',
          seasonality: tour.seasonality || {
            greenSeason: true,
            drySeason: true,
            bestMonths: []
          },
          itinerary: Array.isArray(tour.itinerary) ? tour.itinerary : [],
          fitnessRequirements: tour.fitnessRequirements || {
            level: 'Moderate',
            description: 'Basic fitness required',
            walkingDistance: '2-3 km daily',
            terrain: 'Mostly flat with some uneven ground',
            ageRestrictions: 'Suitable for ages 8+',
            medicalConditions: []
          },
          equipment: tour.equipment || {
            provided: [],
            recommended: [],
            required: []
          },
          groupOptions: Array.isArray(tour.groupOptions) ? tour.groupOptions : [],
          specialFeatures: Array.isArray(tour.specialFeatures) ? tour.specialFeatures : [],
          difficultyDetails: tour.difficultyDetails || 'Standard difficulty level',
          createdAt: tour.createdAt || new Date(),
          updatedAt: tour.updatedAt || new Date()
        })) as Tour[];

        // Filter active tours and limit to 8 for the scroll section
        let activeTours = allTours
          .filter((tour) => tour.status === 'active')
          .slice(0, 8);

        // If no active tours, create sample data for demo
        if (activeTours.length === 0) {
          console.log('No active tours found, using sample data');
          activeTours = [
            {
              id: 'sample-1',
              title: 'Serengeti Safari Adventure',
              description: 'Delve into the safari that takes you through Tanzania\'s most famous national parks, offering unparalleled wildlife viewing, including the Great Migration and Big Five encounters.',
              price: 2850,
              duration: '7 DAYS',
              location: 'Serengeti National Park, Tanzania',
              destinations: ['Serengeti', 'Ngorongoro Crater'],
              activities: ['Wildebeest Migration', 'Big Five', 'Luxury Lodges'],
              accommodations: ['Safari Lodge', 'Luxury Tented Camp'],
              maxGroupSize: 12,
              minGroupSize: 2,
              difficulty: 'moderate' as const,
              includes: ['Accommodation', 'All Meals', 'Park Fees', 'Professional Guide'],
              excludes: ['International Flights', 'Visa Fees', 'Personal Expenses'],
              images: ['https://images.unsplash.com/photo-1551632436-cbf8dd35adfa?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80'],
              featured: false,
              status: 'active' as const,
              rating: 4.8,
              reviewCount: 124,
              tourType: 'standard' as const,
              category: 'Wildlife Safari',
              accommodationLevel: 'Luxury',
              seasonality: {
                greenSeason: true,
                drySeason: true,
                bestMonths: ['June', 'July', 'August', 'September', 'October']
              },
              itinerary: [],
              fitnessRequirements: {
                level: 'Moderate',
                description: 'Basic fitness required',
                walkingDistance: '2-3 km daily',
                terrain: 'Mostly flat with some uneven ground',
                ageRestrictions: 'Suitable for ages 8+',
                medicalConditions: []
              },
              equipment: {
                provided: [],
                recommended: [],
                required: []
              },
              groupOptions: [],
              specialFeatures: ['Wildebeest Migration', 'Big Five', 'Luxury Lodges'],
              difficultyDetails: 'Moderate physical activity with game drives and short walks',
              createdAt: new Date() as any,
              updatedAt: new Date() as any
            },
            {
              id: 'sample-2',
              title: 'Ngorongoro Crater Experience',
              description: 'A shorter safari focusing on two of Tanzania\'s most unique ecosystems, ideal for travelers with limited time but a desire for incredible wildlife encounters.',
              price: 1650,
              duration: '5 DAYS',
              location: 'Ngorongoro Crater, Tanzania',
              destinations: ['Ngorongoro Crater', 'Lake Manyara'],
              activities: ['Crater Floor', 'Rhino Spotting', 'Bird Watching'],
              accommodations: ['Crater Lodge', 'Safari Lodge'],
              maxGroupSize: 15,
              minGroupSize: 2,
              difficulty: 'easy' as const,
              includes: ['Accommodation', 'All Meals', 'Park Fees', 'Professional Guide'],
              excludes: ['International Flights', 'Visa Fees', 'Personal Expenses'],
              images: ['https://images.unsplash.com/photo-1516426122078-c23e76319801?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80'],
              featured: false,
              status: 'active' as const,
              rating: 4.6,
              reviewCount: 89,
              tourType: 'standard' as const,
              category: 'Wildlife Safari',
              accommodationLevel: 'Standard',
              seasonality: {
                greenSeason: true,
                drySeason: true,
                bestMonths: ['June', 'July', 'August', 'September', 'December', 'January']
              },
              itinerary: [],
              fitnessRequirements: {
                level: 'Easy',
                description: 'Minimal physical activity required',
                walkingDistance: '1-2 km daily',
                terrain: 'Flat crater floor',
                ageRestrictions: 'Suitable for all ages',
                medicalConditions: []
              },
              equipment: {
                provided: [],
                recommended: [],
                required: []
              },
              groupOptions: [],
              specialFeatures: ['Crater Floor', 'Rhino Spotting', 'Bird Watching'],
              difficultyDetails: 'Easy crater floor game drives',
              createdAt: new Date() as any,
              updatedAt: new Date() as any
            },
            {
              id: 'sample-3',
              title: 'Maasai Mara Cultural Safari',
              description: 'Experience authentic wildlife and cultural visits in Kenya\'s most famous reserve, combining game drives with traditional Maasai village experiences.',
              price: 1950,
              duration: '5 DAYS',
              location: 'Maasai Mara National Reserve, Kenya',
              destinations: ['Maasai Mara', 'Cultural Villages'],
              activities: ['Cultural Visits', 'Game Drives', 'Balloon Safari'],
              accommodations: ['Safari Lodge', 'Luxury Tented Camp'],
              maxGroupSize: 8,
              minGroupSize: 2,
              difficulty: 'moderate' as const,
              includes: ['Accommodation', 'All Meals', 'Park Fees', 'Professional Guide', 'Cultural Visits'],
              excludes: ['International Flights', 'Visa Fees', 'Personal Gear'],
              images: ['https://images.unsplash.com/photo-1547036967-23d11aacaee0?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80'],
              featured: false,
              status: 'active' as const,
              rating: 4.9,
              reviewCount: 89,
              tourType: 'standard' as const,
              category: 'Adventure',
              accommodationLevel: 'Basic',
              seasonality: {
                greenSeason: false,
                drySeason: true,
                bestMonths: ['June', 'July', 'August', 'September', 'January', 'February']
              },
              itinerary: [],
              fitnessRequirements: {
                level: 'Moderate',
                description: 'Basic fitness required for cultural experiences',
                walkingDistance: '3-5 km daily',
                terrain: 'Flat savanna with village walks',
                ageRestrictions: 'Suitable for ages 12+',
                medicalConditions: []
              },
              equipment: {
                provided: [],
                recommended: [],
                required: []
              },
              groupOptions: [],
              specialFeatures: ['Cultural Visits', 'Game Drives', 'Balloon Safari'],
              difficultyDetails: 'Moderate physical activity with cultural experiences',
              createdAt: new Date() as any,
              updatedAt: new Date() as any
            },
            {
              id: 'sample-4',
              title: 'Amboseli Elephant Paradise',
              description: 'Kilimanjaro views & giants - experience massive elephant herds against the backdrop of Africa\'s highest mountain in this iconic park.',
              price: 1200,
              duration: '3 DAYS',
              location: 'Amboseli National Park, Kenya',
              destinations: ['Amboseli', 'Kilimanjaro Views'],
              activities: ['Elephant Herds', 'Kilimanjaro Views', 'Maasai Culture'],
              accommodations: ['Safari Lodge', 'Tented Camp'],
              maxGroupSize: 12,
              minGroupSize: 2,
              difficulty: 'easy' as const,
              includes: ['Accommodation', 'All Meals', 'Park Fees', 'Professional Guide'],
              excludes: ['International Flights', 'Visa Fees', 'Personal Expenses'],
              images: ['https://images.unsplash.com/photo-1564760055775-d63b17a55c44?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80'],
              featured: false,
              status: 'active' as const,
              rating: 4.7,
              reviewCount: 98,
              tourType: 'standard' as const,
              category: 'Wildlife Safari',
              accommodationLevel: 'Standard',
              seasonality: {
                greenSeason: true,
                drySeason: true,
                bestMonths: ['June', 'July', 'August', 'September', 'January', 'February']
              },
              itinerary: [],
              fitnessRequirements: {
                level: 'Easy',
                description: 'Minimal physical activity required',
                walkingDistance: '1-2 km daily',
                terrain: 'Flat savanna',
                ageRestrictions: 'Suitable for all ages',
                medicalConditions: []
              },
              equipment: {
                provided: [],
                recommended: [],
                required: []
              },
              groupOptions: [],
              specialFeatures: ['Elephant Herds', 'Kilimanjaro Views', 'Maasai Culture'],
              difficultyDetails: 'Easy game drives with cultural experiences',
              createdAt: new Date() as any,
              updatedAt: new Date() as any
            },
            {
              id: 'sample-5',
              title: 'Tarangire Baobab Adventure',
              description: 'Land of giants & baobabs - incredible bird watching and elephant paradise among ancient baobab trees in Tanzania\'s hidden gem.',
              price: 2100,
              duration: '6 DAYS',
              location: 'Tarangire National Park, Tanzania',
              destinations: ['Tarangire', 'Baobab Valley'],
              activities: ['Baobab Trees', 'Elephant Paradise', 'Bird Watching'],
              accommodations: ['Safari Lodge', 'Luxury Tented Camp'],
              maxGroupSize: 10,
              minGroupSize: 2,
              difficulty: 'moderate' as const,
              includes: ['Accommodation', 'All Meals', 'Park Fees', 'Professional Guide'],
              excludes: ['International Flights', 'Visa Fees', 'Personal Expenses'],
              images: ['https://images.unsplash.com/photo-1549366021-9f761d040a94?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80'],
              featured: false,
              status: 'active' as const,
              rating: 4.8,
              reviewCount: 142,
              tourType: 'standard' as const,
              category: 'Wildlife Safari',
              accommodationLevel: 'Luxury',
              seasonality: {
                greenSeason: true,
                drySeason: true,
                bestMonths: ['June', 'July', 'August', 'September', 'October']
              },
              itinerary: [],
              fitnessRequirements: {
                level: 'Moderate',
                description: 'Basic fitness required',
                walkingDistance: '2-3 km daily',
                terrain: 'Mostly flat with some uneven ground',
                ageRestrictions: 'Suitable for ages 8+',
                medicalConditions: []
              },
              equipment: {
                provided: [],
                recommended: [],
                required: []
              },
              groupOptions: [],
              specialFeatures: ['Baobab Trees', 'Elephant Paradise', 'Bird Watching'],
              difficultyDetails: 'Moderate game drives with walking safaris',
              createdAt: new Date() as any,
              updatedAt: new Date() as any
            },
            {
              id: 'sample-6',
              title: 'Lake Manyara Tree Climbing Lions',
              description: 'Discover the famous tree-climbing lions and diverse ecosystems of Lake Manyara, from groundwater forests to alkaline soda lake.',
              price: 1450,
              duration: '4 DAYS',
              location: 'Lake Manyara National Park, Tanzania',
              destinations: ['Lake Manyara', 'Groundwater Forest'],
              activities: ['Tree Climbing Lions', 'Flamingo Watching', 'Forest Walks'],
              accommodations: ['Lodge', 'Tented Camp'],
              maxGroupSize: 14,
              minGroupSize: 2,
              difficulty: 'easy' as const,
              includes: ['Accommodation', 'All Meals', 'Park Fees', 'Professional Guide'],
              excludes: ['International Flights', 'Visa Fees', 'Personal Expenses'],
              images: ['https://images.unsplash.com/photo-1534177616072-ef7dc120449d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80'],
              featured: false,
              status: 'active' as const,
              rating: 4.5,
              reviewCount: 76,
              tourType: 'standard' as const,
              category: 'Wildlife Safari',
              accommodationLevel: 'Standard',
              seasonality: {
                greenSeason: true,
                drySeason: true,
                bestMonths: ['June', 'July', 'August', 'September', 'November', 'December']
              },
              itinerary: [],
              fitnessRequirements: {
                level: 'Easy',
                description: 'Light walking required',
                walkingDistance: '1-2 km daily',
                terrain: 'Forest paths and flat areas',
                ageRestrictions: 'Suitable for all ages',
                medicalConditions: []
              },
              equipment: {
                provided: [],
                recommended: [],
                required: []
              },
              groupOptions: [],
              specialFeatures: ['Tree Climbing Lions', 'Flamingo Watching', 'Forest Walks'],
              difficultyDetails: 'Easy forest walks and game drives',
              createdAt: new Date() as any,
              updatedAt: new Date() as any
            },
            {
              id: 'sample-7',
              title: 'Samburu Unique Wildlife',
              description: 'Experience the unique wildlife of northern Kenya including Grevy\'s zebras, reticulated giraffes, and Somali ostriches in this special reserve.',
              price: 1800,
              duration: '4 DAYS',
              location: 'Samburu National Reserve, Kenya',
              destinations: ['Samburu', 'Ewaso Nyiro River'],
              activities: ['Unique Species', 'River Wildlife', 'Cultural Visits'],
              accommodations: ['Safari Lodge', 'Riverside Camp'],
              maxGroupSize: 10,
              minGroupSize: 2,
              difficulty: 'moderate' as const,
              includes: ['Accommodation', 'All Meals', 'Park Fees', 'Professional Guide'],
              excludes: ['International Flights', 'Visa Fees', 'Personal Expenses'],
              images: ['https://images.unsplash.com/photo-1571771019784-3ff35f4f4277?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80'],
              featured: false,
              status: 'active' as const,
              rating: 4.7,
              reviewCount: 63,
              tourType: 'standard' as const,
              category: 'Wildlife Safari',
              accommodationLevel: 'Standard',
              seasonality: {
                greenSeason: false,
                drySeason: true,
                bestMonths: ['June', 'July', 'August', 'September', 'January', 'February']
              },
              itinerary: [],
              fitnessRequirements: {
                level: 'Moderate',
                description: 'Basic fitness required',
                walkingDistance: '2-4 km daily',
                terrain: 'Semi-arid terrain',
                ageRestrictions: 'Suitable for ages 10+',
                medicalConditions: []
              },
              equipment: {
                provided: [],
                recommended: [],
                required: []
              },
              groupOptions: [],
              specialFeatures: ['Unique Species', 'River Wildlife', 'Cultural Visits'],
              difficultyDetails: 'Moderate game drives in semi-arid environment',
              createdAt: new Date() as any,
              updatedAt: new Date() as any
            },
            {
              id: 'sample-8',
              title: 'Tsavo East & West Adventure',
              description: 'Explore Kenya\'s largest national park, famous for red elephants, diverse landscapes, and the legendary man-eaters of Tsavo.',
              price: 1350,
              duration: '5 DAYS',
              location: 'Tsavo National Parks, Kenya',
              destinations: ['Tsavo East', 'Tsavo West', 'Mzima Springs'],
              activities: ['Red Elephants', 'Mzima Springs', 'Diverse Landscapes'],
              accommodations: ['Safari Lodge', 'Bush Camp'],
              maxGroupSize: 16,
              minGroupSize: 2,
              difficulty: 'easy' as const,
              includes: ['Accommodation', 'All Meals', 'Park Fees', 'Professional Guide'],
              excludes: ['International Flights', 'Visa Fees', 'Personal Expenses'],
              images: ['https://images.unsplash.com/photo-1516426122078-c23e76319801?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80'],
              featured: false,
              status: 'active' as const,
              rating: 4.4,
              reviewCount: 112,
              tourType: 'standard' as const,
              category: 'Wildlife Safari',
              accommodationLevel: 'Basic',
              seasonality: {
                greenSeason: true,
                drySeason: true,
                bestMonths: ['June', 'July', 'August', 'September', 'December', 'January']
              },
              itinerary: [],
              fitnessRequirements: {
                level: 'Easy',
                description: 'Minimal physical activity required',
                walkingDistance: '1-2 km daily',
                terrain: 'Flat savanna and springs',
                ageRestrictions: 'Suitable for all ages',
                medicalConditions: []
              },
              equipment: {
                provided: [],
                recommended: [],
                required: []
              },
              groupOptions: [],
              specialFeatures: ['Red Elephants', 'Mzima Springs', 'Diverse Landscapes'],
              difficultyDetails: 'Easy game drives with spring visits',
              createdAt: new Date() as any,
              updatedAt: new Date() as any
            }
          ];
        }

        console.log('Setting tours for scroll section:', activeTours);
        setTours(activeTours);
      } catch (err) {
        console.error('Error fetching tours:', err);
        setError('Failed to load tours');
      } finally {
        setLoading(false);
      }
    };

    fetchTours();
  }, []);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 900);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  useEffect(() => {
    if (isMobile || !cardsPanelRef.current || !cardsWrapperRef.current) return;

    const cardsPanel = cardsPanelRef.current;
    const cardsWrapper = cardsWrapperRef.current;

    let state = {
      isDragging: false,
      startY: 0,
      startTranslate: 0,
      currentTranslate: 0,
      lastTranslate: 0,
      velocity: 0,
      animationFrame: 0,
    };

    const getPositionY = (e: MouseEvent | TouchEvent) => 
      e.type.includes('mouse') ? (e as MouseEvent).pageY : (e as TouchEvent).touches[0].clientY;

    function dragStart(e: MouseEvent | TouchEvent) {
      if(e.type === 'touchstart') e.preventDefault();

      state.isDragging = true;
      state.startY = getPositionY(e);
      state.startTranslate = state.currentTranslate;
      state.velocity = 0;
      
      cardsPanel.classList.add('grabbing');
      
      cancelAnimationFrame(state.animationFrame);
      state.animationFrame = requestAnimationFrame(animationLoop);
    }

    function drag(e: MouseEvent | TouchEvent) {
      if (!state.isDragging) return;
      const currentY = getPositionY(e);
      const diff = currentY - state.startY;
      state.currentTranslate = state.startTranslate + diff;
    }

    function dragEnd() {
      if (!state.isDragging) return;
      state.isDragging = false;
      cardsPanel.classList.remove('grabbing');
      
      cancelAnimationFrame(state.animationFrame);
      state.animationFrame = requestAnimationFrame(momentumLoop);
    }
    
    function animationLoop() {
      if (!state.isDragging) return;
      
      const moved = state.currentTranslate - state.lastTranslate;
      state.velocity = moved * 5;
      state.lastTranslate = state.currentTranslate;

      setTranslate(false);
      state.animationFrame = requestAnimationFrame(animationLoop);
    }

    function momentumLoop() {
      state.currentTranslate += state.velocity;
      state.velocity *= 0.94;

      if (setTranslate(true) || Math.abs(state.velocity) < 0.1) {
        cancelAnimationFrame(state.animationFrame);
        return;
      }
      
      state.animationFrame = requestAnimationFrame(momentumLoop);
    }
    
    function setTranslate(snapToBounds: boolean) {
      const panelHeight = cardsPanel.offsetHeight;
      const wrapperHeight = cardsWrapper.scrollHeight;
      const maxTranslate = 0;
      const minTranslate = panelHeight - wrapperHeight < 0 ? panelHeight - wrapperHeight : 0;
      
      let boundaryHit = false;

      if (snapToBounds) {
        if (state.currentTranslate > maxTranslate) {
          state.currentTranslate = maxTranslate;
          boundaryHit = true;
        } else if (state.currentTranslate < minTranslate) {
          state.currentTranslate = minTranslate;
          boundaryHit = true;
        }
      } else {
        if (state.currentTranslate > maxTranslate) {
          state.currentTranslate = maxTranslate + (state.currentTranslate - maxTranslate) * 0.4;
        } else if (state.currentTranslate < minTranslate) {
          state.currentTranslate = minTranslate + (state.currentTranslate - minTranslate) * 0.4;
        }
      }

      cardsWrapper.style.transform = `translateY(${state.currentTranslate}px)`;
      return boundaryHit;
    }

    // Event listeners
    cardsPanel.addEventListener('mousedown', dragStart as EventListener);
    window.addEventListener('mousemove', drag as EventListener);
    window.addEventListener('mouseup', dragEnd);
    
    cardsPanel.addEventListener('touchstart', dragStart as EventListener, { passive: false });
    window.addEventListener('touchmove', drag as EventListener, { passive: false });
    window.addEventListener('touchend', dragEnd);
    
    // Prevent default dragging
    const preventDrag = (e: Event) => e.preventDefault();
    cardsWrapper.querySelectorAll('img, a').forEach(el => {
      el.addEventListener('dragstart', preventDrag);
    });

    return () => {
      cardsPanel.removeEventListener('mousedown', dragStart as EventListener);
      window.removeEventListener('mousemove', drag as EventListener);
      window.removeEventListener('mouseup', dragEnd);
      cardsPanel.removeEventListener('touchstart', dragStart as EventListener);
      window.removeEventListener('touchmove', drag as EventListener);
      window.removeEventListener('touchend', dragEnd);
      cardsWrapper.querySelectorAll('img, a').forEach(el => {
        el.removeEventListener('dragstart', preventDrag);
      });
      cancelAnimationFrame(state.animationFrame);
    };
  }, [isMobile]);

  return (
    <div className="relative w-full bg-[#F5F1EB] overflow-hidden">
      {/* Section Height: Match other sections with responsive heights */}
      <div 
        className="w-full"
        style={{
          height: isMobile ? 'auto' : '100vh',
          minHeight: isMobile ? '600px' : '100vh'
        }}
      >
        {/* Main Container: Grid layout like the original */}
        <div 
          className={`
            w-full h-full
            ${isMobile ? 'flex flex-col' : 'grid grid-cols-[45%_55%]'}
          `}
        >
          {/* Left Intro Panel */}
          <div 
            className={`
              flex justify-center items-center
              ${isMobile ? 'text-center py-16 px-8' : 'px-[5vw]'}
            `}
          >
            <div className={`${isMobile ? 'w-full' : 'max-w-[450px]'}`}>
              <span 
                className="block font-['Montserrat',sans-serif] text-xs tracking-[0.2em] font-bold text-[#4B4237] opacity-80 uppercase mb-4"
              >
                Unforgettable Journeys
              </span>
              <h1
                className="font-['Cormorant_Garamond'] text-[clamp(2.5rem,4vw,3.2rem)] leading-[1.2] my-6 text-[#3C352D] font-normal"
              >
                Our Classic Safaris,<br/>
                Timeless Journeys,<br/>
                <em className="opacity-70">Masterfully Curated.</em>
              </h1>
              <p 
                className={`
                  text-sm mb-8 leading-relaxed text-[#4B4237]
                  ${isMobile ? 'mx-auto' : ''}
                  max-w-[40ch]
                `}
              >
                Imagine Africa, perfectly planned. Our expert team meticulously crafts classic safari tours, 
                ensuring every detail is seamless. These tried and tested itineraries mean you simply enjoy 
                a luxurious, worry-free adventure.
              </p>
              <button 
                className="inline-block bg-[#B95E2D] text-[#F5F1EB] font-['Montserrat',sans-serif] font-bold text-xs tracking-[0.15em] py-4 px-8 uppercase transition-colors duration-300 hover:bg-[#a05126] border-none cursor-pointer"
              >
                Explore More
              </button>
            </div>
          </div>

          {/* Right Cards Panel */}
          <div 
            ref={cardsPanelRef}
            className={`
              ${isMobile ? 'h-auto overflow-visible cursor-default select-auto' : 'h-full overflow-hidden cursor-grab select-none'}
              ${!isMobile ? 'hover:cursor-grab active:cursor-grabbing' : ''}
            `}
            style={{
              userSelect: isMobile ? 'auto' : 'none'
            }}
          >
            <div 
              ref={cardsWrapperRef}
              className="will-change-transform"
              style={{
                padding: isMobile ? '0 0 2rem 0' : '2.5rem 0',
                transform: isMobile ? 'none' : undefined
              }}
            >
              {loading ? (
                <div className="flex justify-center items-center h-64">
                  <Loader2 className="h-8 w-8 animate-spin text-[#B95E2D]" />
                </div>
              ) : error ? (
                <div className="flex flex-col items-center justify-center h-64 text-center px-8">
                  <AlertTriangle className="h-12 w-12 text-[#B95E2D] mb-4" />
                  <p className="text-[#4B4237] text-sm">{error}</p>
                </div>
              ) : (
                tours.map((tour, index) => (
                  <div
                    key={tour.id}
                    className={`${isMobile ? 'w-[90%]' : 'w-4/5'} max-w-[400px] mx-auto mb-8 shadow-[0_10px_30px_rgba(0,0,0,0.05)] overflow-hidden rounded`}
                  >
                    <div className="relative">
                      <img
                        src={tour.images && tour.images.length > 0 ? tour.images[0] : 'https://images.unsplash.com/photo-1516426122078-c23e76319801?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80'}
                        alt={tour.title}
                        className="w-full h-[250px] object-cover block"
                        draggable={false}
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.src = 'https://images.unsplash.com/photo-1516426122078-c23e76319801?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80';
                        }}
                      />
                      {/* Price Badge */}
                      <div className="absolute top-4 right-4 bg-[#B95E2D] text-[#F5F1EB] px-3 py-1 rounded-full">
                        <span className="font-['Montserrat',sans-serif] text-xs font-bold">
                          ${tour.price.toLocaleString()}
                        </span>
                      </div>
                    </div>
                    <div className="bg-[#EAE3D6] p-6">
                      {/* Duration */}
                      <span className="font-sans text-[0.65rem] tracking-[0.15em] font-bold text-[#4B4237] opacity-80 block mb-2 uppercase">
                        {tour.duration}
                      </span>

                      {/* Title */}
                      <h2 className="font-['Cormorant_Garamond'] text-2xl leading-[1.3] text-[#3C352D] mb-3 font-normal">
                        {tour.title}
                      </h2>

                      {/* Description */}
                      <p className="font-sans text-[0.85rem] text-[#4B4237] leading-relaxed mb-4">
                        {tour.description.length > 120
                          ? `${tour.description.substring(0, 120)}...`
                          : tour.description
                        }
                      </p>

                      {/* Activity Badges */}
                      {tour.activities && tour.activities.length > 0 && (
                        <div className="flex flex-wrap gap-2 mt-4">
                          {tour.activities.slice(0, 3).map((activity, activityIndex) => (
                            <span
                              key={activityIndex}
                              className="inline-block bg-[#D4C2A4] text-[#3C352D] px-2 py-1 rounded-full text-xs font-['Montserrat',sans-serif] font-medium"
                            >
                              {activity}
                            </span>
                          ))}
                          {tour.activities.length > 3 && (
                            <span className="inline-block bg-[#4B4237] text-[#F5F1EB] px-2 py-1 rounded-full text-xs font-['Montserrat',sans-serif] font-medium">
                              +{tour.activities.length - 3} more
                            </span>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Custom CSS for grabbing state */}
      <style jsx>{`
        .grabbing {
          cursor: grabbing !important;
        }
        
        @import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@400;700&family=Playfair+Display:wght@400;700&display=swap');
      `}</style>
    </div>
  );
};

export default TourScrollSection;
